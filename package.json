{"name": "lax<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx scripts/seed.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@react-spring/web": "^10.0.0", "@tabler/icons-react": "^3.33.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "express-rate-limit": "^7.5.0", "framer-motion": "^11.18.2", "gsap": "^3.13.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.510.0", "mongoose": "^8.15.1", "motion": "^12.12.1", "multer": "^2.0.0", "next": "15.3.2", "next-themes": "^0.4.6", "ogl": "^1.0.11", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sharp": "^0.34.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "task-master-ai": "^0.16.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "case-sensitive-paths-webpack-plugin": "^2.4.0", "dotenv": "^16.5.0", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}